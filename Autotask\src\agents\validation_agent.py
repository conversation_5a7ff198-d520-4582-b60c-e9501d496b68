"""
Validation agent for verifying classification results from the classification agent.
This agent validates that classification results contain correct values and formats.
"""

import json
from typing import Dict, List, Optional, Tuple
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.database import SnowflakeConnection


class ValidationAgent:
    """
    Validates classification results from the classification agent.
    Ensures all required fields are present with valid values according to reference data.
    """

    def __init__(self, db_connection: SnowflakeConnection, reference_data: Dict):
        """
        Initialize the validation agent.

        Args:
            db_connection (SnowflakeConnection): Database connection
            reference_data (dict): Reference data for valid classification values
        """
        self.db_connection = db_connection
        self.reference_data = reference_data
        
        # Required classification fields that must be validated
        self.required_fields = ["ISSUETYPE", "SUBISSUETYPE", "TICKETCATEGORY", "TICKETTYPE", "PRIORITY", "STATUS"]
        
        # Maximum retry attempts for classification
        self.max_retry_attempts = 3

    def validate_classification_result(self, classified_data: Dict) -> <PERSON>ple[bool, List[str]]:
        """
        Validates the classification result from the classification agent.

        Args:
            classified_data (dict): Classification result to validate

        Returns:
            tuple: (is_valid: bool, validation_errors: List[str])
        """
        if not classified_data:
            return False, ["Classification result is empty or None"]

        validation_errors = []

        # Check if all required fields are present
        for field in self.required_fields:
            if field not in classified_data:
                validation_errors.append(f"Missing required field: {field}")
                continue

            field_data = classified_data[field]
            
            # Check if field data has correct structure
            if not isinstance(field_data, dict):
                validation_errors.append(f"Field {field} must be a dictionary with 'Value' and 'Label' keys")
                continue

            # Check if Value and Label keys exist
            if "Value" not in field_data:
                validation_errors.append(f"Field {field} missing 'Value' key")
                continue
                
            if "Label" not in field_data:
                validation_errors.append(f"Field {field} missing 'Label' key")
                continue

            # Check if Value is valid according to reference data
            field_value = str(field_data["Value"])
            field_label = field_data["Label"]
            
            # Validate against reference data
            field_name_lower = field.lower()
            if field_name_lower in self.reference_data:
                valid_values = self.reference_data[field_name_lower]
                
                if field_value not in valid_values:
                    validation_errors.append(
                        f"Invalid value '{field_value}' for field {field}. "
                        f"Valid values: {list(valid_values.keys())}"
                    )
                    continue
                
                # Check if label matches the value
                expected_label = valid_values[field_value]
                if field_label != expected_label:
                    validation_errors.append(
                        f"Label mismatch for field {field}. "
                        f"Expected '{expected_label}' but got '{field_label}'"
                    )
            else:
                validation_errors.append(f"No reference data found for field {field}")

        # Return validation result
        is_valid = len(validation_errors) == 0
        return is_valid, validation_errors

    def validate_and_retry_classification(self, new_ticket_data: Dict, extracted_metadata: Dict, 
                                        similar_tickets: List[Dict], ai_processor, 
                                        model: str = 'mixtral-8x7b') -> Optional[Dict]:
        """
        Validates classification result and retries up to 3 times if validation fails.

        Args:
            new_ticket_data (dict): New ticket data
            extracted_metadata (dict): Extracted metadata
            similar_tickets (list): List of similar tickets
            ai_processor: AI processor instance for classification
            model (str): LLM model to use for classification

        Returns:
            dict: Valid classification data or None if all attempts failed
        """
        print("\n--- Starting Classification Validation Process ---")
        
        for attempt in range(1, self.max_retry_attempts + 1):
            print(f"\n🔍 Classification Attempt {attempt}/{self.max_retry_attempts}")
            
            # Get classification result
            classified_data = ai_processor.classify_ticket(
                new_ticket_data, extracted_metadata, similar_tickets, model
            )
            
            if not classified_data:
                print(f"❌ Attempt {attempt}: Classification failed - no result returned")
                if attempt < self.max_retry_attempts:
                    print("🔄 Retrying classification...")
                    continue
                else:
                    print("❌ All classification attempts failed - no result returned")
                    return None
            
            print(f"📋 Attempt {attempt} - Classified Ticket Data:")
            print(json.dumps(classified_data, indent=2))
            
            # Validate the classification result
            is_valid, validation_errors = self.validate_classification_result(classified_data)
            
            if is_valid:
                print(f"✅ Validation successful on attempt {attempt}")
                print("🎯 Classification result is valid and ready for next agent")
                return classified_data
            else:
                print(f"❌ Validation failed on attempt {attempt}")
                print("🚨 Validation errors found:")
                for error in validation_errors:
                    print(f"   • {error}")
                
                if attempt < self.max_retry_attempts:
                    print("🔄 Retrying classification due to validation errors...")
                else:
                    print("❌ Maximum retry attempts reached - classification validation failed")
                    print("🚨 Final validation errors:")
                    for error in validation_errors:
                        print(f"   • {error}")
                    return None
        
        return None

    def get_validation_summary(self, classified_data: Dict) -> str:
        """
        Generate a summary of the validation results for logging.

        Args:
            classified_data (dict): Classification data to summarize

        Returns:
            str: Validation summary
        """
        if not classified_data:
            return "❌ Validation Summary: No classification data provided"
        
        is_valid, validation_errors = self.validate_classification_result(classified_data)
        
        if is_valid:
            summary = "✅ Validation Summary: All fields validated successfully\n"
            summary += "📊 Validated Fields:\n"
            for field in self.required_fields:
                if field in classified_data:
                    field_data = classified_data[field]
                    summary += f"   • {field}: {field_data.get('Value')} ({field_data.get('Label')})\n"
            return summary
        else:
            summary = f"❌ Validation Summary: {len(validation_errors)} validation errors found\n"
            summary += "🚨 Errors:\n"
            for error in validation_errors:
                summary += f"   • {error}\n"
            return summary
