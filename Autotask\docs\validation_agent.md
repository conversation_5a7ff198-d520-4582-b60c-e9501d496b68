# ValidationAgent Documentation

## Overview

The `ValidationAgent` is a new component that validates the classification results from the classification agent. It ensures that all classification outputs are correct and match the expected values before passing them to the next agent in the workflow.

## Key Features

### 1. **Automatic Validation**
- Validates all required classification fields: `ISSUETYPE`, `SUBISSUETYPE`, `TICKETCATEGORY`, `TICKETTYPE`, `PRIORITY`, `STATUS`
- Checks that each field has the correct structure with `Value` and `Label` keys
- Verifies that values exist in the reference data
- Ensures labels match the expected labels for each value

### 2. **Retry Logic**
- Automatically retries classification up to **3 times** if validation fails
- Provides detailed error messages for each validation failure
- Only passes validated results to the next agent

### 3. **Error Handling**
- Returns `None` if all 3 attempts fail
- Logs detailed error messages explaining what went wrong
- Provides clear feedback about validation status

## Integration

The ValidationAgent is integrated into the main `IntakeClassificationAgent` in the `process_new_ticket` method:

```python
# Before (old code):
classified_data = self.classify_ticket(new_ticket_data, extracted_metadata, similar_tickets, model=classify_model)

# After (new code with validation):
classified_data = self.validation_agent.validate_and_retry_classification(
    new_ticket_data, extracted_metadata, similar_tickets, self.ai_processor, model=classify_model
)
```

## Validation Process Flow

1. **Classification Attempt**: The AI processor attempts to classify the ticket
2. **Validation Check**: The ValidationAgent validates the classification result
3. **Success Path**: If valid, the result is passed to the next agent
4. **Retry Path**: If invalid, the process retries (up to 3 times total)
5. **Failure Path**: If all attempts fail, returns `None` and logs errors

## Example Output

### Successful Validation
```
🔍 Classification Attempt 1/3
📋 Attempt 1 - Classified Ticket Data:
{
  "ISSUETYPE": {"Value": "6", "Label": "Network"},
  "SUBISSUETYPE": {"Value": "29", "Label": "Network"},
  "TICKETCATEGORY": {"Value": "3", "Label": "Standard"},
  "TICKETTYPE": {"Value": "2", "Label": "Incident"},
  "STATUS": {"Value": "1", "Label": "New"},
  "PRIORITY": {"Value": "1", "Label": "High"}
}
✅ Validation successful on attempt 1
🎯 Classification result is valid and ready for next agent
```

### Failed Validation with Retry
```
🔍 Classification Attempt 1/3
❌ Validation failed on attempt 1
🚨 Validation errors found:
   • Missing required field: PRIORITY
   • Missing required field: STATUS
🔄 Retrying classification due to validation errors...
```

### Maximum Retries Reached
```
❌ Maximum retry attempts reached - classification validation failed
🚨 Final validation errors:
   • Invalid value '999' for field ISSUETYPE
   • Missing required field: SUBISSUETYPE
```

## Files Modified

1. **`src/agents/validation_agent.py`** - New ValidationAgent class
2. **`src/agents/intake_agent.py`** - Integration with main intake agent

## Benefits

- **Reliability**: Ensures only valid classification results proceed in the workflow
- **Error Recovery**: Automatically retries failed classifications
- **Transparency**: Provides clear logging of validation status and errors
- **Maintainability**: Centralized validation logic that's easy to modify or extend
